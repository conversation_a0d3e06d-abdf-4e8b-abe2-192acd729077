"""
Gemini AI Service
Integrates with Google's Gemini API to provide NCERT-aligned educational content generation
"""

import google.generativeai as genai
import json
import os
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
from pathlib import Path

from config import get_config, MODEL_CONFIGS
from pdf_extractor import NCERTPDFExtractor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GeminiService:
    def __init__(self, api_key: Optional[str] = None):
        """Initialize Gemini service with API key"""
        self.config = get_config()
        self.api_key = api_key or self.config.GEMINI_API_KEY
        
        if not self.api_key:
            raise ValueError("Gemini API key is required")
        
        # Configure Gemini
        genai.configure(api_key=self.api_key)
        
        # Initialize PDF extractor
        self.pdf_extractor = NCERTPDFExtractor(self.config.DATA_DIR)
        
        # Ensure PDF data is extracted
        self._ensure_pdf_data_extracted()
        
        # Load NCERT data
        self.ncert_data = self._load_ncert_data()
        
        # Initialize models
        self.models = {}
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize Gemini models for different tasks"""
        try:
            for task, config in MODEL_CONFIGS.items():
                if config['model_name'].startswith('gemini'):
                    self.models[task] = genai.GenerativeModel(
                        model_name=config['model_name'],
                        generation_config=genai.types.GenerationConfig(
                            max_output_tokens=config.get('max_tokens', 2048),
                            temperature=config.get('temperature', 0.7),
                            top_p=config.get('top_p', 0.9)
                        )
                    )
            logger.info("Gemini models initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing Gemini models: {e}")
            raise
    
    def _ensure_pdf_data_extracted(self):
        """Ensure PDF data is extracted and available"""
        try:
            # Check if Physics 11th PDF data exists
            physics_11_path = Path(self.config.NCERT_DATA_DIR) / "grade_11" / "physics" / "chapters_from_pdf.json"
            
            if not physics_11_path.exists():
                logger.info("PDF data not found, extracting from PDFs...")
                self.pdf_extractor.extract_all_ncert_data()
            else:
                logger.info("PDF data already extracted and available")
                
        except Exception as e:
            logger.error(f"Error ensuring PDF data extraction: {e}")
    
    def _load_ncert_data(self) -> Dict:
        """Load NCERT curriculum data"""
        ncert_data = {}
        try:
            # Load curriculum mapping
            mapping_path = Path(self.config.NCERT_DATA_DIR) / "curriculum_mapping.json"
            if mapping_path.exists():
                with open(mapping_path, 'r', encoding='utf-8') as f:
                    ncert_data['curriculum_mapping'] = json.load(f)
            
            # Load grade-specific data
            ncert_dir = Path(self.config.NCERT_DATA_DIR)
            for grade_dir in ncert_dir.glob("grade_*"):
                if grade_dir.is_dir():
                    grade_num = grade_dir.name.split('_')[1]
                    ncert_data[f'grade_{grade_num}'] = {}
                    
                    for subject_dir in grade_dir.iterdir():
                        if subject_dir.is_dir():
                            subject_data = {}
                            
                            # Load chapters (try PDF-extracted data first)
                            pdf_chapters_file = subject_dir / "chapters_from_pdf.json"
                            chapters_file = subject_dir / "chapters.json"
                            
                            if pdf_chapters_file.exists():
                                with open(pdf_chapters_file, 'r', encoding='utf-8') as f:
                                    subject_data['chapters'] = json.load(f)
                                logger.info(f"Loaded PDF-extracted data for Grade {grade_num} {subject_dir.name}")
                            elif chapters_file.exists():
                                with open(chapters_file, 'r', encoding='utf-8') as f:
                                    subject_data['chapters'] = json.load(f)
                                logger.info(f"Loaded manual data for Grade {grade_num} {subject_dir.name}")
                            
                            ncert_data[f'grade_{grade_num}'][subject_dir.name] = subject_data
            
            logger.info(f"Loaded NCERT data for {len(ncert_data)} components")
            return ncert_data
            
        except Exception as e:
            logger.error(f"Error loading NCERT data: {e}")
            return {}
    
    def get_ncert_context(self, grade: int, subject: str, topic: Optional[str] = None, language: str = "english") -> str:
        """Get relevant NCERT context for given parameters with language support"""
        context_parts = []

        try:
            # Add comprehensive physics curriculum context
            physics_curriculum = self._get_comprehensive_physics_context(grade, language)
            context_parts.append(physics_curriculum)

            # Try to load language-specific data first
            grade_key = f'grade_{grade}'

            # Look for language-specific data files
            ncert_dir = Path(self.config.NCERT_DATA_DIR)
            language_file = ncert_dir / grade_key / subject / f"chapters_from_pdf_{language.lower()}.json"

            if language_file.exists():
                try:
                    with open(language_file, 'r', encoding='utf-8') as f:
                        language_data = json.load(f)

                    context_parts.append(f"Grade {grade} {subject.title()} Content ({language.title()}):")
                    chapters_list = language_data.get('chapters', [])

                    # Filter and enhance chapters based on topic
                    relevant_chapters = []
                    for chapter in chapters_list:
                        chapter_info = self._process_chapter_data(chapter, topic, language)
                        if chapter_info:
                            relevant_chapters.append(chapter_info)

                    # Limit to most relevant chapters
                    context_parts.extend(relevant_chapters[:5])

                except Exception as e:
                    logger.error(f"Error loading language-specific data: {e}")
                    # Fall back to comprehensive context
                    return physics_curriculum
            else:
                # Use comprehensive context if no specific file
                logger.info(f"No language-specific file found, using comprehensive context")

            return "\n\n".join(context_parts)

        except Exception as e:
            logger.error(f"Error getting NCERT context: {e}")
            return self._get_comprehensive_physics_context(grade, language)

    def _get_comprehensive_physics_context(self, grade: int, language: str) -> str:
        """Get comprehensive physics context for Class 11"""
        if language.lower() in ['hi', 'hindi']:
            return """
NCERT भौतिक विज्ञान कक्षा 11 पाठ्यक्रम:

भाग 1:
अध्याय 1: भौतिक जगत - भौतिकी का परिचय, मौलिक बल, प्रकृति के नियम
अध्याय 2: मात्रक और मापन - SI मात्रक, आयाम विश्लेषण, त्रुटि विश्लेषण
अध्याय 3: सरल रेखा में गति - स्थिति, वेग, त्वरण, गति के समीकरण
अध्याय 4: समतल में गति - प्रक्षेप्य गति, वृत्तीय गति, सापेक्ष वेग
अध्याय 5: गति के नियम - न्यूटन के नियम, घर्षण, वृत्तीय गति की गतिकी
अध्याय 6: कार्य, ऊर्जा और शक्ति - कार्य-ऊर्जा प्रमेय, संरक्षण नियम
अध्याय 7: कणों के निकाय और घूर्णी गति - द्रव्यमान केंद्र, कोणीय गति
अध्याय 8: गुरुत्वाकर्षण - न्यूटन का गुरुत्वाकर्षण नियम, केप्लर के नियम

भाग 2:
अध्याय 9: ठोसों के यांत्रिक गुण - प्रत्यास्थता, प्रतिबल और विकृति
अध्याय 10: तरलों के यांत्रिक गुण - दाब, पास्कल का नियम, बर्नूली का सिद्धांत
अध्याय 11: द्रव्य के तापीय गुण - तापमान, ऊष्मा, अवस्था परिवर्तन
अध्याय 12: ऊष्मागतिकी - ऊष्मागतिकी के नियम, कार्नो इंजन
अध्याय 13: अणुगति सिद्धांत - गैसों का व्यवहार, गतिज सिद्धांत
अध्याय 14: दोलन - सरल आवर्त गति, लोलक, तरंग गति
अध्याय 15: तरंगें - तरंग के प्रकार, ध्वनि तरंगें, डॉप्लर प्रभाव
"""
        else:
            return """
NCERT Physics Class 11 Curriculum:

Part 1:
Chapter 1: Physical World - Introduction to Physics, Fundamental Forces, Laws of Nature
Chapter 2: Units and Measurements - SI Units, Dimensional Analysis, Error Analysis
Chapter 3: Motion in a Straight Line - Position, Velocity, Acceleration, Equations of Motion
Chapter 4: Motion in a Plane - Projectile Motion, Circular Motion, Relative Velocity
Chapter 5: Laws of Motion - Newton's Laws, Friction, Dynamics of Circular Motion
Chapter 6: Work, Energy and Power - Work-Energy Theorem, Conservation Laws
Chapter 7: System of Particles and Rotational Motion - Centre of Mass, Angular Momentum
Chapter 8: Gravitation - Newton's Law of Gravitation, Kepler's Laws

Part 2:
Chapter 9: Mechanical Properties of Solids - Elasticity, Stress and Strain
Chapter 10: Mechanical Properties of Fluids - Pressure, Pascal's Law, Bernoulli's Principle
Chapter 11: Thermal Properties of Matter - Temperature, Heat, Change of State
Chapter 12: Thermodynamics - Laws of Thermodynamics, Carnot Engine
Chapter 13: Kinetic Theory - Behavior of Gases, Kinetic Theory
Chapter 14: Oscillations - Simple Harmonic Motion, Pendulum, Wave Motion
Chapter 15: Waves - Types of Waves, Sound Waves, Doppler Effect

Key Topics for Quiz Generation:
- Motion in a Straight Line: Displacement, velocity, acceleration, equations of motion
- Laws of Motion: Newton's three laws, friction, circular motion dynamics
- Work, Energy and Power: Work-energy theorem, conservation of energy
- Gravitation: Universal law of gravitation, orbital motion
- Oscillations: Simple harmonic motion, pendulum
- Waves: Wave properties, sound waves, Doppler effect
"""

    def _process_chapter_data(self, chapter: Dict, topic: Optional[str], language: str) -> Optional[str]:
        """Process individual chapter data and return formatted info"""
        try:
            # Handle different chapter data structures
            if 'title' in chapter:
                chapter_title = chapter['title']
            elif 'filename' in chapter:
                chapter_title = chapter['filename'].replace('.pdf', '').replace('_', ' ').title()
            else:
                chapter_title = f"Chapter {chapter.get('chapter_number', 'Unknown')}"

            # Skip if title is too generic or malformed
            if len(chapter_title) < 3 or 'CONTENT' in chapter_title.upper():
                return None

            chapter_info = f"Chapter: {chapter_title}"

            # Check if this chapter matches the topic
            topic_match = False
            if topic:
                topic_match = (topic.lower() in chapter_title.lower() or
                             any(topic.lower() in section.get('title', '').lower()
                                 for section in chapter.get('sections', [])))

            if topic_match or not topic:
                # Include detailed info for matching topic or all if no specific topic
                if 'sections' in chapter and chapter['sections']:
                    valid_sections = [s.get('title', '') for s in chapter['sections'][:5]
                                    if s.get('title') and len(s.get('title', '')) > 3]
                    if valid_sections:
                        chapter_info += f"\nSections: {', '.join(valid_sections)}"

                # Add content if available
                if 'sections' in chapter:
                    content_parts = []
                    for section in chapter['sections'][:3]:
                        if section.get('content') and len(section.get('content', '')) > 10:
                            content_parts.append(section['content'][:200])

                    if content_parts:
                        chapter_info += f"\nContent Preview: {' ... '.join(content_parts)}"

                return chapter_info

            return None

        except Exception as e:
            logger.error(f"Error processing chapter data: {e}")
            return None

    def _get_default_ncert_context(self, grade: int, subject: str, topic: Optional[str] = None) -> str:
        """Fallback method for getting NCERT context when language-specific data is not available"""
        context_parts = []

        # Get grade-specific data
        grade_key = f'grade_{grade}'
        if grade_key in self.ncert_data and subject in self.ncert_data[grade_key]:
            subject_data = self.ncert_data[grade_key][subject]

            if 'chapters' in subject_data:
                chapters_data = subject_data['chapters']
                context_parts.append(f"Grade {grade} {subject.title()} Content:")

                # Handle both PDF-extracted and manual data structures
                if 'chapters' in chapters_data:
                    chapters_list = chapters_data['chapters']
                else:
                    chapters_list = chapters_data.get('chapters', [])

                for chapter in chapters_list:
                    # Handle different chapter data structures
                    if 'title' in chapter:
                        chapter_title = chapter['title']
                    elif 'filename' in chapter:
                        chapter_title = chapter['filename'].replace('.pdf', '').replace('_', ' ').title()
                    else:
                        chapter_title = f"Chapter {chapter.get('chapter_number', 'Unknown')}"

                    chapter_info = f"Chapter: {chapter_title}"

                    # Check if this chapter matches the topic
                    topic_match = (topic and
                                 (topic.lower() in chapter_title.lower() or
                                  any(topic.lower() in section.get('title', '').lower()
                                      for section in chapter.get('sections', []))))

                    if topic_match or not topic:
                        # Include detailed info for matching topic or all if no specific topic
                        if 'sections' in chapter and chapter['sections']:
                            section_titles = [s.get('title', '') for s in chapter['sections'][:5]]
                            chapter_info += f"\nSections: {', '.join(filter(None, section_titles))}"

                        if 'key_concepts' in chapter and chapter['key_concepts']:
                            chapter_info += f"\nKey Concepts: {', '.join(chapter['key_concepts'][:10])}"
                        elif 'topics' in chapter and chapter['topics']:
                            chapter_info += f"\nTopics: {', '.join(chapter['topics'][:10])}"

                        if 'examples' in chapter and chapter['examples']:
                            chapter_info += f"\nExamples Available: {len(chapter['examples'])}"

                        if 'exercises' in chapter and chapter['exercises']:
                            chapter_info += f"\nExercise Questions: {len(chapter['exercises'])}"

                    context_parts.append(chapter_info)

        return "\n\n".join(context_parts)
    
    def generate_quiz(self, input_data: Dict) -> Dict:
        """Generate quiz using Gemini API with NCERT context and language support"""
        try:
            # Extract parameters
            subject = input_data.get('subject', '')
            topic = input_data.get('topic', '')
            grade = input_data.get('grade', 10)
            question_count = input_data.get('questionCount', 10)
            difficulty = input_data.get('difficulty', 'medium')
            question_types = input_data.get('questionTypes', ['mcq'])
            language = input_data.get('language', 'english').lower()

            # Normalize subject for processing
            normalized_subject = self._normalize_subject(subject, language)

            # Get NCERT context with language support
            ncert_context = self.get_ncert_context(grade, normalized_subject, topic, language)

            # Create prompt with language support
            prompt = self._create_quiz_prompt(
                subject, topic, grade, question_count,
                difficulty, question_types, ncert_context, language
            )

            # Generate using Gemini
            model = self.models.get('quiz_generation')
            if not model:
                raise ValueError("Quiz generation model not initialized")

            response = model.generate_content(prompt)

            # Parse response
            quiz_data = self._parse_quiz_response(
                response.text, subject, topic, grade, difficulty, language
            )

            return {
                "success": True,
                "data": quiz_data,
                "generated_at": datetime.now().isoformat(),
                "model": "gemini-1.5-flash",
                "ncert_aligned": True,
                "language": language
            }

        except Exception as e:
            logger.error(f"Error generating quiz: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": None
            }
    
    def _create_quiz_prompt(self, subject: str, topic: str, grade: int,
                          question_count: int, difficulty: str,
                          question_types: List[str], ncert_context: str, language: str = "english") -> str:
        """Create detailed prompt for quiz generation with language support"""

        # Language-specific instructions
        language_instructions = {
            "english": {
                "instruction": "Generate all content in English only",
                "format_note": "Use English for all questions, options, explanations, and JSON keys. Do not mix languages.",
                "sample_question": "What is the velocity of an object moving at constant speed?",
                "sample_options": ["10 m/s", "20 m/s", "30 m/s", "40 m/s"]
            },
            "hindi": {
                "instruction": "Generate all content in Hindi only (केवल हिन्दी में सभी सामग्री तैयार करें)",
                "format_note": "Use Hindi (Devanagari script) for all questions, options, and explanations. Keep JSON structure in English but content in Hindi.",
                "sample_question": "स्थिर गति से चलने वाली वस्तु का वेग क्या है?",
                "sample_options": ["10 मी/से", "20 मी/से", "30 मी/से", "40 मी/से"]
            },
            "en": {
                "instruction": "Generate all content in English only",
                "format_note": "Use English for all questions, options, explanations, and JSON keys. Do not mix languages.",
                "sample_question": "What is the velocity of an object moving at constant speed?",
                "sample_options": ["10 m/s", "20 m/s", "30 m/s", "40 m/s"]
            },
            "hi": {
                "instruction": "Generate all content in Hindi only (केवल हिन्दी में सभी सामग्री तैयार करें)",
                "format_note": "Use Hindi (Devanagari script) for all questions, options, and explanations. Keep JSON structure in English but content in Hindi.",
                "sample_question": "स्थिर गति से चलने वाली वस्तु का वेग क्या है?",
                "sample_options": ["10 मी/से", "20 मी/से", "30 मी/से", "40 मी/से"]
            }
        }

        lang_config = language_instructions.get(language, language_instructions["english"])

        prompt = f"""
You are an expert NCERT-aligned educator. Generate a {difficulty} level quiz for Class {grade} students.

CRITICAL LANGUAGE REQUIREMENT: {lang_config["instruction"]}
{lang_config["format_note"]}

EXAMPLE FORMAT FOR {language.upper()}:
Question: {lang_config["sample_question"]}
Options: {lang_config["sample_options"]}

NCERT CONTEXT:
{ncert_context}

QUIZ REQUIREMENTS:
- Subject: {subject}
- Chapter: {topic}
- Class: {grade}
- Number of questions: {question_count}
- Difficulty: {difficulty}
- Question types: {', '.join(question_types)}
- Language: {language}

STRICT LANGUAGE RULES:
1. If language is "hindi" or "hi": ALL content must be in Hindi (Devanagari script)
2. If language is "english" or "en": ALL content must be in English
3. Do NOT mix languages in the same quiz
4. Technical terms can remain in English if commonly used that way
5. Units (m/s, kg, etc.) can remain in standard notation

CONTENT GUIDELINES:
1. All questions MUST be aligned with NCERT curriculum and textbooks
2. Use terminology and concepts exactly as presented in NCERT books
3. Ensure questions test conceptual understanding, not just memorization
4. Include a mix of direct questions and application-based problems
5. Reference specific NCERT chapter content when relevant

For each question, provide:
1. Question text (clear and unambiguous in specified language)
2. Question type (mcq, true_false, short_answer, numerical)
3. For MCQ: 4 options with one correct answer (in specified language)
4. For True/False: statement with correct answer (in specified language)
5. For Short Answer: expected answer length and key points
6. For Numerical: step-by-step solution approach
7. Points value (1-5 based on difficulty and complexity)
8. Detailed explanation referencing NCERT content (in specified language)
9. NCERT chapter/section reference if applicable

OUTPUT FORMAT:
Return a valid JSON object with the following structure:
{{
    "title": "Quiz title in {language}",
    "subject": "{subject}",
    "topic": "{topic}",
    "grade": {grade},
    "difficulty": "{difficulty}",
    "language": "{language}",
    "questions": [
        {{
            "question": "Question text in {language}",
            "type": "question_type",
            "options": ["option1 in {language}", "option2 in {language}", "option3 in {language}", "option4 in {language}"],
            "correct_answer": "correct option in {language}",
            "points": 1-5,
            "explanation": "Detailed explanation in {language}",
            "ncert_reference": "Chapter X, Section Y",
            "bloom_level": "remember/understand/apply/analyze"
        }}
    ],
    "total_points": "sum of all question points",
    "estimated_time": "estimated time in minutes",
    "ncert_alignment": true
}}

REMEMBER: Generate ALL content in {language} language only. Do not mix languages.

Generate the quiz now:
"""
        return prompt
    
    def _parse_quiz_response(self, response_text: str, subject: str,
                           topic: str, grade: int, difficulty: str, language: str = "english") -> Dict:
        """Parse Gemini response into structured quiz format with language support"""
        try:
            # Try to parse as JSON first
            if response_text.strip().startswith('{'):
                quiz_data = json.loads(response_text)
                # Ensure language is set
                quiz_data["language"] = language
                return quiz_data

            # If not JSON, extract JSON from response
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1

            if json_start != -1 and json_end != -1:
                json_text = response_text[json_start:json_end]
                quiz_data = json.loads(json_text)
                # Ensure language is set
                quiz_data["language"] = language
                return quiz_data

            # Fallback: create structured quiz from text
            return self._create_fallback_quiz(response_text, subject, topic, grade, difficulty, language)

        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing error: {e}")
            return self._create_fallback_quiz(response_text, subject, topic, grade, difficulty, language)
    
    def _create_fallback_quiz(self, text: str, subject: str, topic: str,
                            grade: int, difficulty: str, language: str = "english") -> Dict:
        """Create fallback quiz structure when parsing fails"""

        # Normalize language
        lang_key = "hindi" if language.lower() in ["hi", "hindi"] else "english"

        # Language-specific fallback content
        fallback_content = {
            "english": {
                "title": f"Class {grade} {subject.title()} Quiz: {topic}",
                "question": f"What is the main concept in {topic}?",
                "options": ["Option A", "Option B", "Option C", "Option D"],
                "explanation": "This tests basic understanding of the concept.",
                "reference": f"Class {grade} {subject} textbook"
            },
            "hindi": {
                "title": f"कक्षा {grade} {self._get_subject_hindi(subject)} प्रश्नोत्तरी: {topic}",
                "question": f"{topic} में मुख्य अवधारणा क्या है?",
                "options": ["विकल्प A", "विकल्प B", "विकल्प C", "विकल्प D"],
                "explanation": "यह अवधारणा की बुनियादी समझ का परीक्षण करता है।",
                "reference": f"कक्षा {grade} {self._get_subject_hindi(subject)} पाठ्यपुस्तक"
            }
        }

        content = fallback_content.get(lang_key, fallback_content["english"])

        return {
            "title": content["title"],
            "subject": subject,
            "topic": topic,
            "grade": grade,
            "difficulty": difficulty,
            "language": language,
            "questions": [
                {
                    "question": content["question"],
                    "type": "mcq",
                    "options": content["options"],
                    "correct_answer": content["options"][0],
                    "points": 1,
                    "explanation": content["explanation"],
                    "ncert_reference": content["reference"],
                    "bloom_level": "understand"
                }
            ],
            "total_points": 1,
            "estimated_time": 5,
            "ncert_alignment": True
        }

    def _get_subject_hindi(self, subject: str) -> str:
        """Get Hindi name for subject"""
        subject_mapping = {
            "physics": "भौतिक विज्ञान",
            "chemistry": "रसायन विज्ञान",
            "mathematics": "गणित",
            "biology": "जीव विज्ञान",
            "economics": "अर्थशास्त्र",
            "भौतिक विज्ञान": "भौतिक विज्ञान",
            "रसायन विज्ञान": "रसायन विज्ञान",
            "गणित": "गणित",
            "जीव विज्ञान": "जीव विज्ञान",
            "अर्थशास्त्र": "अर्थशास्त्र"
        }
        return subject_mapping.get(subject.lower(), subject)

    def _normalize_subject(self, subject: str, language: str) -> str:
        """Normalize subject name for consistent processing"""
        # Subject mapping for both directions
        subject_mapping = {
            # English to English (normalized)
            "physics": "physics",
            "chemistry": "chemistry",
            "mathematics": "mathematics",
            "biology": "biology",
            "economics": "economics",
            # Hindi to English
            "भौतिक विज्ञान": "physics",
            "रसायन विज्ञान": "chemistry",
            "गणित": "mathematics",
            "जीव विज्ञान": "biology",
            "अर्थशास्त्र": "economics"
        }

        normalized = subject_mapping.get(subject.lower(), subject.lower())
        return normalized
    
    def generate_curriculum(self, input_data: Dict) -> Dict:
        """Generate curriculum using Gemini API with NCERT alignment"""
        try:
            subject = input_data.get('subject', '')
            grade = input_data.get('grade', 10)
            duration = input_data.get('duration', '1 semester')
            
            # Get NCERT context
            ncert_context = self.get_ncert_context(grade, subject)
            
            prompt = f"""
You are an expert NCERT curriculum designer. Create a comprehensive curriculum plan.

NCERT CONTEXT:
{ncert_context}

CURRICULUM REQUIREMENTS:
- Subject: {subject}
- Grade: {grade}
- Duration: {duration}

Create a detailed curriculum that:
1. Follows NCERT guidelines and sequence
2. Covers all mandatory topics for the grade
3. Includes learning objectives aligned with NCERT outcomes
4. Provides assessment strategies
5. Suggests teaching methodologies
6. Includes timeline and pacing

Return a structured JSON response with curriculum details.
"""
            
            model = self.models.get('curriculum_generation')
            response = model.generate_content(prompt)
            
            return {
                "success": True,
                "data": self._parse_curriculum_response(response.text),
                "generated_at": datetime.now().isoformat(),
                "model": "gemini-1.5-pro",
                "ncert_aligned": True
            }
            
        except Exception as e:
            logger.error(f"Error generating curriculum: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": None
            }
    
    def _parse_curriculum_response(self, response_text: str) -> Dict:
        """Parse curriculum response"""
        try:
            # Try to parse as JSON
            if response_text.strip().startswith('{'):
                return json.loads(response_text)
            
            # Extract JSON if embedded
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start != -1 and json_end != -1:
                json_text = response_text[json_start:json_end]
                return json.loads(json_text)
            
            # Fallback structure
            return {
                "title": "NCERT Aligned Curriculum",
                "description": response_text[:500] + "...",
                "modules": [],
                "assessment_plan": {},
                "resources": []
            }
            
        except Exception as e:
            logger.error(f"Error parsing curriculum response: {e}")
            return {"error": "Failed to parse curriculum response"}
    
    def grade_answer(self, input_data: Dict) -> Dict:
        """Grade student answers using Gemini API"""
        try:
            question = input_data.get('question', '')
            student_answer = input_data.get('student_answer', '')
            correct_answer = input_data.get('correct_answer', '')
            subject = input_data.get('subject', '')
            grade = input_data.get('grade', 10)
            max_points = input_data.get('max_points', 5)
            
            # Get NCERT context for grading criteria
            ncert_context = self.get_ncert_context(grade, subject)
            
            prompt = f"""
You are an expert NCERT-aligned teacher grading student responses.

GRADING CONTEXT:
Subject: {subject}
Grade: {grade}
Max Points: {max_points}

NCERT REFERENCE:
{ncert_context[:1000]}...

QUESTION: {question}

CORRECT ANSWER: {correct_answer}

STUDENT ANSWER: {student_answer}

Grade the student's answer based on:
1. Accuracy of content
2. Use of correct terminology
3. Completeness of response
4. Understanding demonstrated
5. NCERT alignment

Provide:
- Score (0 to {max_points})
- Detailed feedback
- Areas for improvement
- NCERT references for further study

Return as JSON:
{{
    "score": 0-{max_points},
    "feedback": "detailed feedback",
    "strengths": ["strength1", "strength2"],
    "improvements": ["area1", "area2"],
    "ncert_references": ["reference1", "reference2"]
}}
"""
            
            model = self.models.get('grading')
            response = model.generate_content(prompt)
            
            return {
                "success": True,
                "data": self._parse_grading_response(response.text),
                "generated_at": datetime.now().isoformat(),
                "model": "gemini-1.5-flash"
            }
            
        except Exception as e:
            logger.error(f"Error grading answer: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": None
            }
    
    def _parse_grading_response(self, response_text: str) -> Dict:
        """Parse grading response"""
        try:
            if response_text.strip().startswith('{'):
                return json.loads(response_text)
            
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start != -1 and json_end != -1:
                json_text = response_text[json_start:json_end]
                return json.loads(json_text)
            
            # Fallback
            return {
                "score": 0,
                "feedback": "Unable to process grading response",
                "strengths": [],
                "improvements": ["Please review the answer"],
                "ncert_references": []
            }
            
        except Exception as e:
            logger.error(f"Error parsing grading response: {e}")
            return {
                "score": 0,
                "feedback": "Grading error occurred",
                "strengths": [],
                "improvements": [],
                "ncert_references": []
            }
    
    def generate_content(self, input_data: Dict) -> Dict:
        """Generate educational content using Gemini API"""
        try:
            content_type = input_data.get('type', 'explanation')
            subject = input_data.get('subject', '')
            topic = input_data.get('topic', '')
            grade = input_data.get('grade', 10)
            
            # Get NCERT context
            ncert_context = self.get_ncert_context(grade, subject, topic)
            
            prompt = f"""
Generate {content_type} content for NCERT-aligned education.

NCERT CONTEXT:
{ncert_context}

CONTENT REQUEST:
- Type: {content_type}
- Subject: {subject}
- Topic: {topic}
- Grade: {grade}

Create content that:
1. Aligns with NCERT curriculum
2. Uses appropriate language for grade level
3. Includes examples and illustrations
4. Provides clear explanations
5. References NCERT textbook content

Generate comprehensive {content_type} content now.
"""
            
            model = self.models.get('content_generation')
            response = model.generate_content(prompt)
            
            return {
                "success": True,
                "data": {
                    "content": response.text,
                    "type": content_type,
                    "subject": subject,
                    "topic": topic,
                    "grade": grade,
                    "ncert_aligned": True
                },
                "generated_at": datetime.now().isoformat(),
                "model": "gemini-1.5-pro"
            }
            
        except Exception as e:
            logger.error(f"Error generating content: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": None
            }

# Example usage
if __name__ == "__main__":
    service = GeminiService()
    
    # Test quiz generation
    quiz_input = {
        "subject": "mathematics",
        "topic": "Quadratic Equations",
        "grade": 10,
        "questionCount": 5,
        "difficulty": "medium",
        "questionTypes": ["mcq", "short_answer"]
    }
    
    result = service.generate_quiz(quiz_input)
    print("Quiz Generation Result:")
    print(json.dumps(result, indent=2))